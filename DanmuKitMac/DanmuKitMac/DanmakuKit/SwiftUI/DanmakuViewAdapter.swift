//
//  DanmakuViewAdapter.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import SwiftUI

@available(macOS 10.15, *)
/// 将 DanmakuView 适配到 SwiftUI 的 NSViewRepresentable
public struct DanmakuViewAdapter: NSViewRepresentable {
    
    public typealias NSViewType = DanmakuView
    
    @ObservedObject var coordinator: Coordinator
    
    public init(coordinator: Coordinator) {
        self.coordinator = coordinator
    }
    
    /// 创建 NSView（由协调器生成）
    public func makeNSView(context: Context) -> NSViewType {
        return coordinator.makeView()
    }

    /// 更新 NSView（此处无需额外处理）
    public func updateNSView(_ nsView: NSViewType, context: Context) {}

    /// 创建协调器
    public func makeCoordinator() -> Coordinator {
        return coordinator
    }
    
    public class Coordinator: ObservableObject {
        
        public init() {}
        
        /// 内部持有的 DanmakuView 实例
        public private(set) var danmakuView: DanmakuView?

        private var frameObserver: Any?

        /// 外部设置的 DanmakuViewDelegate
        public weak var danmakuViewDelegate: DanmakuViewDelegate? {
            willSet {
                danmakuView?.delegate = newValue
            }
        }
        
        /// 播放弹幕
        public func play() {
            danmakuView?.play()
        }

        /// 暂停弹幕
        public func pause() {
            danmakuView?.pause()
        }

        /// 停止弹幕并清理资源
        public func stop() {
            danmakuView?.stop()
        }

        /// 清空当前已显示的弹幕
        public func clean() {
            danmakuView?.clean()
        }

        /// 发射一条弹幕
        public func shoot(danmaku: DanmakuCellModel) {
            danmakuView?.shoot(danmaku: danmaku)
        }

        /// 判断当前是否可以发射此条弹幕
        public func canShoot(danmaku: DanmakuCellModel) -> Bool {
            guard let view = danmakuView else { return false }
            return view.canShoot(danmaku: danmaku)
        }

        /// 重新计算轨道（在尺寸/配置变化时调用）
        public func recalculateTracks() {
            danmakuView?.recalculateTracks()
        }

        /// 同步显示某条弹幕（指定进度）
        public func sync(danmaku: DanmakuCellModel, at progress: Float) {
            danmakuView?.sync(danmaku: danmaku, at: progress)
        }
        
        func makeView() -> DanmakuView {
            danmakuView = DanmakuView(frame: .zero)
            // Note: NSView doesn't have a publisher for frame changes like UIView
            // We could use KVO or other observation methods if needed
            return danmakuView!
        }
        
    }
    
}
