//
//  DanmakuCellModel.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

public enum DanmakuCellType {
    /// 右向左滚动的“漂浮”弹幕
    case floating
    /// 顶部固定弹幕
    case top
    /// 底部固定弹幕
    case bottom
}

public protocol DanmakuCellModel {

    /// 对应的弹幕 Cell 类型（需继承 DanmakuCell）
    var cellClass: DanmakuCell.Type { get }

    /// 弹幕尺寸（用于布局与轨道判定）
    var size: CGSize { get }

    /// 弹幕所在轨道索引（内部设置，外部可读取）
    var track: UInt? { get set }

    /// 整体显示时长（秒）
    var displayTime: Double { get }

    /// 弹幕类型（漂浮/顶部/底部）
    var type: DanmakuCellType { get }

    /// 唯一标识符（用于查找/去重/控制）
    var identifier: String { get }

    /// 判断两个弹幕模型是否相等
    /// - Parameter cellModel: 另一个弹幕模型
    func isEqual(to cellModel: DanmakuCellModel) -> Bool

}
