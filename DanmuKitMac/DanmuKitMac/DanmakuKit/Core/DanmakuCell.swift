//
//  DanmakuCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

open class DanmakuCell: NSView {

    /// 绑定的弹幕数据模型
    public var model: DanmakuCellModel?

    /// 动画已播放的时长（由内部维护）
    public internal(set) var animationTime: TimeInterval = 0

    /// 动画起始的时间戳（内部使用）
    var animationBeginTime: TimeInterval = 0

    public override func makeBackingLayer() -> CALayer {
        return DanmakuAsyncLayer()
    }

    public override var wantsLayer: Bool {
        get { return true }
        set { super.wantsLayer = newValue }
    }

    public required override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        wantsLayer = true
        setupLayer()
    }

    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    /// 可重写：内容绘制前的回调
    open func willDisplay() {}


    /// 可重写：绘制弹幕内容
    /// - Parameters:
    ///   - context: 绘制上下文
    ///   - size: 视图尺寸（bounds.size）
    ///   - isCancelled: 是否被取消
    open func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {}

    /// 可重写：内容绘制完成后的回调
    /// - Parameter finished: 若绘制被取消则为 false
    open func didDisplay(_ finished: Bool) {}

    /// 可重写：弹幕进入轨道时机
    open func enterTrack() {}

    /// 可重写：弹幕离开轨道时机
    open func leaveTrack() {}

    /// 是否使用异步渲染
    public var displayAsync = true {
        didSet {
            guard let layer = layer as? DanmakuAsyncLayer else { return }
            layer.displayAsync = displayAsync
        }
    }

    /// 触发重绘（会调用 displaying(_:_:_:)）
    public func redraw() {
        layer?.setNeedsDisplay()
    }

}

extension DanmakuCell {
    
    var realFrame: CGRect {
        if let presentation = layer?.presentation() {
            return presentation.frame
        } else {
            return frame
        }
    }
    
    func setupLayer() {
        guard let layer = layer as? DanmakuAsyncLayer else { return }
        
        layer.contentsScale = NSScreen.main?.backingScaleFactor ?? 1.0
        
        layer.willDisplay = { [weak self] (layer) in
            guard let strongSelf = self else { return }
            strongSelf.willDisplay()
        }
        
        layer.displaying = { [weak self] (context, size, isCancelled) in
            guard let strongSelf = self else { return }
            strongSelf.displaying(context, size, isCancelled())
        }
        
        layer.didDisplay = { [weak self] (layer, finished) in
            guard let strongSelf = self else { return }
            strongSelf.didDisplay(finished)
        }
    }
    
}
