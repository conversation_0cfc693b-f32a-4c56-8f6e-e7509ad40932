//
//  DanmakuTextCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku cell
//

import Cocoa

public class DanmakuTextCell: DanmakuCell {
    
    public override func willDisplay() {
        super.willDisplay()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled, let model = model as? DanmakuTextCellModel else { return }

        // 可配置的描边+阴影（默认都为 0，相当于纯文字，与 iOS 视觉一致）
        NSGraphicsContext.saveGraphicsState()
        let nsContext = NSGraphicsContext(cgContext: context, flipped: false)
        NSGraphicsContext.current = nsContext
        defer { NSGraphicsContext.restoreGraphicsState() }

        let text = NSString(string: model.text)

        let attributes: [NSAttributedString.Key: Any] = [
            .font: model.font,
            .foregroundColor: model.textColor
        ]

        // 先描边
        if model.strokeWidth > 0 || model.strokeOpacity > 0 {
            context.setLineWidth(max(0, model.strokeWidth))
            context.setLineJoin(.round)
            let strokeColor = model.strokeColor.withAlphaComponent(model.strokeOpacity)
            context.setStrokeColor(strokeColor.cgColor)
            context.setTextDrawingMode(.stroke)
            text.draw(at: .zero, withAttributes: attributes)
        }

        // 再填充 + 阴影（如果需要）
        context.saveGState()
        if model.shadowOpacity > 0 && (model.shadowBlur > 0 || model.shadowOffset != .zero) {
            let shadowColor = model.shadowColor.withAlphaComponent(model.shadowOpacity)
            context.setShadow(offset: model.shadowOffset, blur: model.shadowBlur, color: shadowColor.cgColor)
        }
        context.setTextDrawingMode(.fill)
        text.draw(at: .zero, withAttributes: attributes)
        context.restoreGState()
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
    }
    
}
