//
//  GifAnimator.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa
import ImageIO

public class GifAnimator {
    // MARK: 公有 API（与 iOS 对齐）
    /// GIF 总帧数（只读）
    public private(set) var frameCount: Int = 0
    /// 循环一遍的总时长（只读）
    public private(set) var loopDuration: TimeInterval = 0
    /// 单帧最大允许时长，默认 1.0s（过大的帧间隔会被裁剪）
    public var maxFrameDuration: TimeInterval = 1.0
    /// 是否后台解码帧图像，默认 true
    public var backgroundDecode: Bool = true
    /// 播放完成回调
    public var didFinishAnimation: (() -> Void)?
    /// 屏幕刷新时的帧图更新回调（macOS 使用 CGImage?）
    public var update: ((CGImage?) -> Void)?

    // MARK: Internal state
    private var imageSource: CGImageSource
    private var preloadCount: Int
    private var imageSize: CGSize
    private var imageScale: CGFloat
    private var maxRepeatCount: Int

    private struct Frame { var image: CGImage?; var duration: TimeInterval }
    private var frames: [Frame] = []

    public private(set) var currentFrameIndex: Int = 0 { didSet { previousFrameIndex = oldValue } }
    private var previousFrameIndex: Int = 0
    private var currentRepeatCount: UInt = 0

    public var isLastFrame: Bool { return currentFrameIndex == max(0, frameCount - 1) }
    public var currentFrameImage: CGImage? { return frames.indices.contains(currentFrameIndex) ? frames[currentFrameIndex].image : nil }
    public var currentFrameDuration: TimeInterval { return frames.indices.contains(currentFrameIndex) ? frames[currentFrameIndex].duration : .infinity }

    // MARK: Timing
    private var displayLink: CVDisplayLink?
    private var lastFrameTime: CFTimeInterval = 0
    private var timeSinceLastFrameChange: TimeInterval = 0

    // MARK: Queue
    private static var pool: DanmakuQueuePool?
    private static func createPoolIfNeeded() {
        guard pool == nil else { return }
        pool = DanmakuQueuePool(name: "com.DanmakuKitMac.GifAnimator", queueCount: 8, qos: .userInteractive)
    }
    private lazy var queue: DispatchQueue = {
        Self.createPoolIfNeeded(); return Self.pool!.queue
    }()

    // MARK: Inits
    /// 使用 GIF 数据直接构造动画器
    public convenience init?(data: Data) {
        guard let source = CGImageSourceCreateWithData(data as CFData, nil) else { return nil }
        self.init(imageSource: source, preloadCount: 10, imageSize: .zero, imageScale: NSScreen.main?.backingScaleFactor ?? 2.0, maxRepeatCount: .max)
    }

    /// 通过 CGImageSource 构造动画器
    /// - Parameters:
    ///   - source: 图片源（GIF）
    ///   - count: 预加载帧数量
    ///   - size: 目标图像尺寸（.zero 表示保持原始）
    ///   - scale: 屏幕 scale
    ///   - repeatCount: 最大重复次数
    public init(imageSource source: CGImageSource,
                preloadCount count: Int,
                imageSize size: CGSize,
                imageScale scale: CGFloat,
                maxRepeatCount repeatCount: Int) {
        self.imageSource = source
        self.preloadCount = count
        self.imageSize = size
        self.imageScale = scale
        self.maxRepeatCount = repeatCount
    }

    deinit { if let dl = displayLink { CVDisplayLinkStop(dl) }; displayLink = nil }

    // MARK: Prepare/Play/Stop
    /// 准备 GIF 资源（解析帧与持续时间）
    public func prepare() {
        frameCount = CGImageSourceGetCount(imageSource)
        frames.removeAll(keepingCapacity: true)
        frames.reserveCapacity(max(0, frameCount))
        queue.async { [weak self] in self?.setupAsync() }
    }

    /// 开始播放
    public func startAnimation() {
        guard displayLink == nil else { return }
        var link: CVDisplayLink?
        guard CVDisplayLinkCreateWithActiveCGDisplays(&link) == kCVReturnSuccess, let dl = link else { return }
        CVDisplayLinkSetOutputCallback(dl, { _, inNow, _, _, _, ctx in
            let animator = Unmanaged<GifAnimator>.fromOpaque(ctx!).takeUnretainedValue()
            let ts = CFTimeInterval(inNow.pointee.videoTime) / CFTimeInterval(inNow.pointee.videoTimeScale)
            animator.onScreenUpdate(timestamp: ts)
            return kCVReturnSuccess
        }, Unmanaged.passUnretained(self).toOpaque())
        CVDisplayLinkStart(dl)
        displayLink = dl
        lastFrameTime = CACurrentMediaTime()
    }

    /// 停止播放并回调 didFinishAnimation
    public func stopAnimation() {
        guard let dl = displayLink else { return }
        CVDisplayLinkStop(dl)
        displayLink = nil
        currentFrameIndex = 0
        timeSinceLastFrameChange = 0
        didFinishAnimation?()
    }

    // MARK: Internals
    private func setupAsync() {
        var duration: TimeInterval = 0
        let count = frameCount
        for i in 0..<count {
            let frameDur = Self.getFrameDuration(from: imageSource, at: i, maxFrameDuration: maxFrameDuration)
            duration += min(frameDur, maxFrameDuration)
            if i > preloadCount {
                frames.append(Frame(image: nil, duration: frameDur))
            } else {
                frames.append(Frame(image: decodeFrame(at: i), duration: frameDur))
            }
        }
        loopDuration = duration
    }

    private func onScreenUpdate(timestamp ts: CFTimeInterval) {
        let delta = ts - lastFrameTime
        lastFrameTime = ts
        // 累加屏幕更新时长，直到超过当前帧时长
        timeSinceLastFrameChange += min(maxFrameDuration, delta)
        guard timeSinceLastFrameChange >= currentFrameDuration else { return }
        timeSinceLastFrameChange -= currentFrameDuration
        incrementFrameIndex()
        update?(currentFrameImage)
    }

    private func incrementFrameIndex() {
        currentFrameIndex = (currentFrameIndex + 1) % max(1, frameCount)
        if isLastFrame { currentRepeatCount += 1; if currentRepeatCount >= maxRepeatCount { stopAnimation() } }
    }

    private func decodeFrame(at index: Int) -> CGImage? {
        let options: [CFString: Any]? = imageSize == .zero ? nil : [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceShouldCacheImmediately: true,
            kCGImageSourceThumbnailMaxPixelSize: max(imageSize.width, imageSize.height)
        ]
        guard let cg = CGImageSourceCreateThumbnailAtIndex(imageSource, index, options as CFDictionary?) else { return nil }
        if backgroundDecode {
            // 背景强制解码（生成新的 CGImage）
            let width = CGFloat(cg.width) / max(1, imageScale)
            let height = CGFloat(cg.height) / max(1, imageScale)
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            guard let ctx = CGContext(data: nil, width: Int(width * imageScale), height: Int(height * imageScale), bitsPerComponent: 8, bytesPerRow: 0, space: colorSpace, bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else { return cg }
            ctx.interpolationQuality = .high
            ctx.draw(cg, in: CGRect(origin: .zero, size: CGSize(width: width, height: height)))
            return ctx.makeImage()
        }
        return cg
    }

    private func updatePreloadedFrames() {
        let nextStart = (currentFrameIndex + 1)
        let nextEnd = min(frameCount - 1, currentFrameIndex + preloadCount)
        guard nextStart <= nextEnd else { return }
        let indices = Array(nextStart...nextEnd)
        queue.async { [weak self] in
            guard let self = self else { return }
            for i in indices where self.frames[i].image == nil {
                self.frames[i].image = self.decodeFrame(at: i)
            }
        }
    }

    private static func getFrameDuration(from source: CGImageSource, at index: Int, maxFrameDuration: TimeInterval) -> TimeInterval {
        let defaultFrameDuration = 0.1
        guard let props = CGImageSourceCopyPropertiesAtIndex(source, index, nil) as? [String: Any],
              let gif = props[kCGImagePropertyGIFDictionary as String] as? [String: Any] else { return defaultFrameDuration }
        let unclamped = gif[kCGImagePropertyGIFUnclampedDelayTime as String] as? NSNumber
        let clamped = gif[kCGImagePropertyGIFDelayTime as String] as? NSNumber
        let duration = unclamped?.doubleValue ?? clamped?.doubleValue ?? defaultFrameDuration
        // 与 iOS 一致：极小帧时长提升到默认
        let minDur = 0.011
        return min(max(duration > minDur ? duration : defaultFrameDuration, 0), maxFrameDuration)
    }
}
