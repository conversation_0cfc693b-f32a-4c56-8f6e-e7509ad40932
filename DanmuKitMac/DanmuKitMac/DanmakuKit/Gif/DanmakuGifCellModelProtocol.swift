//
//  DanmakuGifCellModelProtocol.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Interface parity with iOS DanmakuKit
//

import Foundation
import CoreGraphics

public protocol DanmakuGifCellModel: DanmakuCellModel {
    /// GIF 数据源（二进制数据）
    var resource: Data? { get set }

    /// 每帧的最小时长，默认 0.1s
    var minFrameDuration: Float { get }

    /// 预加载帧数量，默认 10
    var preloadFrameCount: Int { get }

    /// 动画最大重复次数
    var maxRepeatCount: Int { get }

    /// 是否在后台解码图片，默认 true
    var backgroundDecode: Bool { get }
}

public extension DanmakuGifCellModel {
    /// 默认最小时长 0.1
    var minFrameDuration: Float { 0.1 }
    /// 默认预加载 10 帧
    var preloadFrameCount: Int { 10 }
    /// 默认无限次重复
    var maxRepeatCount: Int { .max }
    /// 默认开启后台解码
    var backgroundDecode: Bool { true }
}

