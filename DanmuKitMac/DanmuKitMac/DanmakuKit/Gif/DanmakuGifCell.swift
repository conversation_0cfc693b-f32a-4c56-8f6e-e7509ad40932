//
//  DanmakuGifCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

/// 用于展示 GIF 的弹幕 Cell（macOS）
public class DanmakuGifCell: DanmakuCell {
    
    private var gifAnimator: GifAnimator?
    private var currentImage: CGImage?
    
    public override func willDisplay() {
        super.willDisplay()
        setupGifAnimation()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled else { return }
        
        if let image = currentImage {
            context.draw(image, in: CGRect(origin: .zero, size: size))
        }
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
        if finished {
            gifAnimator?.startAnimation()
        }
    }
    
    public override func enterTrack() {
        super.enterTrack()
        gifAnimator?.startAnimation()
    }
    
    public override func leaveTrack() {
        super.leaveTrack()
        gifAnimator?.stopAnimation()
        gifAnimator = nil
    }
    
    private func setupGifAnimation() {
        // Support both protocol-conforming models and the previous class name for back-compat
        if let model = model as? DanmakuGifCellModel, let data = model.resource {
            gifAnimator = GifAnimator(data: data)
        } else if let legacy = model as? DanmakuGifCellModelImpl, let data = legacy.resource {
            gifAnimator = GifAnimator(data: data)
        } else {
            return
        }
        gifAnimator?.prepare()
        currentImage = gifAnimator?.currentFrameImage

        gifAnimator?.update = { [weak self] image in
            guard let self = self else { return }
            self.currentImage = image
            self.redraw()
        }
    }
    
    deinit {
        gifAnimator?.stopAnimation()
    }
    
}
