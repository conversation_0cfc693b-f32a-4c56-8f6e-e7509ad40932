//
//  DanmakuQueuePool.swift
//  DanmakuKit
//
//  Created by <PERSON><PERSON> on 2020/8/16.
//

import Foundation

/// 弹幕绘制队列池，用于管理多个并发绘制队列 / Danmaku drawing queue pool for managing multiple concurrent drawing queues
class DanmakuQueuePool {

    /// 队列池名称 / Queue pool name
    public let name: String

    /// 队列数组 / Queue array
    private var queues: [DispatchQueue] = []

    /// 队列数量 / Queue count
    public let queueCount: Int

    /// 计数器，用于轮询分配队列 / Counter for round-robin queue allocation
    private var counter: Int = 0
    
    /// 初始化队列池 / Initialize queue pool
    /// - Parameters:
    ///   - name: 队列名称 / Queue name
    ///   - queueCount: 队列数量 / Queue count
    ///   - qos: 服务质量 / Quality of service
    public init(name: String, queueCount: Int, qos: DispatchQoS) {
        self.name = name
        self.queueCount = queueCount
        for _ in 0..<queueCount {
            let queue = DispatchQueue(label: name, qos: qos, attributes: [], autoreleaseFrequency: .inherit, target: nil)
            queues.append(queue)
        }
    }

    /// 获取下一个可用队列（轮询方式） / Get next available queue (round-robin)
    public var queue: DispatchQueue {
        return getQueue()
    }
    
    /// 轮询获取队列 / Get queue in round-robin fashion
    private func getQueue() -> DispatchQueue {
        if counter == Int.max {
            counter = 0
        }
        let queue = queues[counter % queueCount]
        counter += 1
        return queue
    }
    
}
