//
//  DanmakuCell.swift
//  DanmakuKit
//
//  Created by <PERSON> on 2020/8/16.
//

#if os(iOS) || os(tvOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// 弹幕单元格基类，所有弹幕都应继承此类 / Base class for danmaku cells, all danmaku should inherit from this class
open class DanmakuCell: DanmakuView_BaseView {

    /// 绑定的弹幕数据模型 / Bound danmaku data model
    public var model: DanmakuCellModel?

    /// 动画已播放的时长（由内部维护） / Animation elapsed time (maintained internally)
    public internal(set) var animationTime: TimeInterval = 0

    /// 动画起始的时间戳（内部使用） / Animation start timestamp (internal use)
    var animationBeginTime: TimeInterval = 0

    #if os(iOS) || os(tvOS)
    public override class var layerClass: AnyClass {
        return DanmakuAsyncLayer.self
    }
    #elseif os(macOS)
    public override func makeBackingLayer() -> CALayer {
        return <PERSON><PERSON>kuAsyncLayer()
    }

    public override var wantsLayer: Bool {
        get { return true }
        set { super.wantsLayer = newValue }
    }
    #endif
    
    #if os(iOS) || os(tvOS)
    public required override init(frame: CGRect) {
        super.init(frame: frame)
        setupLayer()
    }
    #elseif os(macOS)
    public required override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        wantsLayer = true
        setupLayer()
    }
    #endif

    public required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    /// 可重写：内容绘制前的回调 / Override: callback before content rendering
    open func willDisplay() {}


    /// 可重写：绘制弹幕内容 / Override: draw danmaku content
    /// - Parameters:
    ///   - context: 绘制上下文 / drawing context
    ///   - size: 视图尺寸（bounds.size） / view size (bounds.size)
    ///   - isCancelled: 是否被取消 / whether drawing is cancelled
    open func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {}

    /// 可重写：内容绘制完成后的回调 / Override: callback after content rendering
    /// - Parameter finished: 若绘制被取消则为 false / false if draw is cancelled
    open func didDisplay(_ finished: Bool) {}

    /// 可重写：弹幕进入轨道时机 / Override: timing when danmaku enters track
    open func enterTrack() {}

    /// 可重写：弹幕离开轨道时机 / Override: timing when danmaku leaves track
    open func leaveTrack() {}
    
    /// 是否使用异步渲染 / Whether to use asynchronous rendering
    public var displayAsync = true {
        didSet {
            guard let layer = layer as? DanmakuAsyncLayer else { return }
            layer.displayAsync = displayAsync
        }
    }

    /// 触发重绘（会调用 displaying(_:_:_:)） / Trigger redraw (will call displaying(_:_:_:))
    public func redraw() {
        #if os(iOS) || os(tvOS)
        layer.setNeedsDisplay()
        #elseif os(macOS)
        layer?.setNeedsDisplay()
        #endif
    }
       
}

extension DanmakuCell {

    var realFrame: CGRect {
        #if os(iOS) || os(tvOS)
        if layer.presentation() != nil {
            return layer.presentation()!.frame
        } else {
            return frame
        }
        #elseif os(macOS)
        if let presentation = layer?.presentation() {
            return presentation.frame
        } else {
            return frame
        }
        #endif
    }

    func setupLayer() {
        guard let layer = layer as? DanmakuAsyncLayer else { return }

        #if os(iOS) || os(tvOS)
        layer.contentsScale = UIScreen.main.scale
        #elseif os(macOS)
        layer.contentsScale = NSScreen.main?.backingScaleFactor ?? 1.0
        #endif

        layer.willDisplay = { [weak self] (layer) in
            guard let strongSelf = self else { return }
            strongSelf.willDisplay()
        }

        layer.displaying = { [weak self] (context, size, isCancelled) in
            guard let strongSelf = self else { return }
            strongSelf.displaying(context, size, isCancelled())
        }

        layer.didDisplay = { [weak self] (layer, finished) in
            guard let strongSelf = self else { return }
            strongSelf.didDisplay(finished)
        }
    }

}
