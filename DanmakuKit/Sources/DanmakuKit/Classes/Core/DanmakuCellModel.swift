//
//  DanmakuCellModel.swift
//  DanmakuKit
//
//  Created by <PERSON> on 2020/8/16.
//

#if os(iOS) || os(tvOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

public enum DanmakuCellType {
    /// 右向左滚动的"漂浮"弹幕 / Right-to-left scrolling "floating" danmaku
    case floating
    /// 顶部固定弹幕 / Top fixed danmaku
    case top
    /// 底部固定弹幕 / Bottom fixed danmaku
    case bottom
}

public protocol DanmakuCellModel {

    /// 对应的弹幕 Cell 类型（需继承 DanmakuCell） / Corresponding danmaku Cell type (must inherit from DanmakuCell)
    var cellClass: DanmakuCell.Type { get }

    /// 弹幕尺寸（用于布局与轨道判定） / Danmaku size (used for layout and track determination)
    var size: CGSize { get }

    /// 弹幕所在轨道索引（内部设置，外部可读取） / Track index for danmaku (set internally, readable externally)
    var track: UInt? { get set }

    /// 整体显示时长（秒） / Total display duration (seconds)
    var displayTime: Double { get }

    /// 弹幕类型（漂浮/顶部/底部） / Danmaku type (floating/top/bottom)
    var type: DanmakuCellType { get }

    /// 唯一标识符（用于查找/去重/控制） / Unique identifier (for finding/deduplication/control)
    var identifier: String { get }

    /// 判断两个弹幕模型是否相等 / Determine if two danmaku models are equal
    /// - Parameter cellModel: 另一个弹幕模型 / Another danmaku model
    func isEqual(to cellModel: DanmakuCellModel) -> Bool

}
