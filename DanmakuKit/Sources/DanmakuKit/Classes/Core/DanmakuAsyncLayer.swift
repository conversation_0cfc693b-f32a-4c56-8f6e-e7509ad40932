//
//  DanmakuAsyncLayer.swift
//  DanmakuKit
//
//  Created by <PERSON> on 2020/8/16.
//

#if os(iOS) || os(tvOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// 哨兵类，用于取消异步绘制任务 / Sentinel class for canceling async drawing tasks
class Sentinel {

    private var value: Int32 = 0

    public func getValue() -> Int32 {
        return value
    }

    public func increase() {
        #if os(iOS) || os(tvOS)
        let p = UnsafeMutablePointer<Int32>.allocate(capacity: 1)
        p.pointee = value
        OSAtomicIncrement32(p)
        p.deallocate()
        #elseif os(macOS)
        _ = OSAtomicIncrement32(&value)
        #endif
    }

}

/// 异步绘制层，用于弹幕的高性能渲染 / Async drawing layer for high-performance danmaku rendering
public class DanmakuAsyncLayer: CALayer {

    /// 是否异步绘制，默认 true（提升性能，减少主线程阻塞） / Whether to draw asynchronously, default true (improves performance, reduces main thread blocking)
    public var displayAsync = true

    /// 即将开始绘制的回调 / Callback before drawing starts
    public var willDisplay: ((_ layer: DanmakuAsyncLayer) -> Void)?

    /// 绘制中回调（提供 CGContext/尺寸/取消标记） / Drawing callback (provides CGContext/size/cancellation flag)
    public var displaying: ((_ context: CGContext, _ size: CGSize, _ isCancelled:(() -> Bool)) -> Void)?

    /// 绘制完成回调（finished 为 false 表示中途取消） / Drawing completion callback (finished = false means cancelled)
    public var didDisplay: ((_ layer: DanmakuAsyncLayer, _ finished: Bool) -> Void)?

    /// 用于绘制弹幕的并发队列数量 / Number of concurrent queues for drawing danmaku
    public static var drawDanmakuQueueCount = 16 {
        didSet {
            guard drawDanmakuQueueCount != oldValue else { return }
            pool = nil
            createPoolIfNeed()
        }
    }
    
    private let sentinel = Sentinel()

    private static var pool: DanmakuQueuePool?

    override init() {
        super.init()
        #if os(iOS) || os(tvOS)
        contentsScale = UIScreen.main.scale
        #elseif os(macOS)
        // macOS will set contentsScale in setupLayer
        #endif
    }

    override init(layer: Any) {
        super.init(layer: layer)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }

    deinit {
        sentinel.increase()
    }
    
    public override func setNeedsDisplay() {
        // 1. 取消上次绘制 / Cancel the last drawing
        #if os(iOS) || os(tvOS)
        sentinel.increase()
        #elseif os(macOS)
        cancelAsyncDisplay()
        #endif
        // 2. 调用父类方法 / Call super
        super.setNeedsDisplay()
    }

    public override func display() {
        #if os(iOS) || os(tvOS)
        display(isAsync: displayAsync)
        #elseif os(macOS)
        super.contents = super.contents
        displayAsync(displayAsync)
        #endif
    }
    
    #if os(iOS) || os(tvOS)
    private func display(isAsync: Bool) {
        guard displaying != nil, bounds.size.width > 0, bounds.size.height > 0 else {
            willDisplay?(self)
            contents = nil
            didDisplay?(self, true)
            return
        }

        if isAsync {
            willDisplay?(self)
            let value = sentinel.getValue()
            let isCancelled = {() -> Bool in
                return value != self.sentinel.getValue()
            }
            let size = bounds.size
            let scale = contentsScale
            let opaque = isOpaque
            let backgroundColor = (opaque && self.backgroundColor != nil) ? self.backgroundColor : nil
            queue.async {
                guard !isCancelled() else { return }
                UIGraphicsBeginImageContextWithOptions(size, opaque, scale)
                guard let context = UIGraphicsGetCurrentContext() else {
                    UIGraphicsEndImageContext()
                    return
                }
                if opaque {
                    context.saveGState()
                    if backgroundColor == nil || (backgroundColor?.alpha ?? 0) < 1 {
                        context.setFillColor(UIColor.white.cgColor)
                        context.addRect(CGRect(x: 0, y: 0, width: size.width * scale, height: size.height * scale))
                        context.fillPath()
                    }
                    if let backgroundColor = backgroundColor {
                        context.setFillColor(backgroundColor)
                        context.addRect(CGRect(x: 0, y: 0, width: size.width * scale, height: size.height * scale))
                        context.fillPath()
                    }
                    context.restoreGState()
                }
                self.displaying?(context, size, isCancelled)
                if isCancelled() {
                    UIGraphicsEndImageContext()
                    DispatchQueue.main.async {
                        self.didDisplay?(self, false)
                    }
                    return
                }
                let image = UIGraphicsGetImageFromCurrentImageContext()
                UIGraphicsEndImageContext()
                if isCancelled() {
                    DispatchQueue.main.async {
                        self.didDisplay?(self, false)
                    }
                    return
                }
                DispatchQueue.main.async {
                    if isCancelled() {
                        self.didDisplay?(self, false)
                    } else {
                        self.contents = image?.cgImage
                        self.didDisplay?(self, true)
                    }
                }
            }

        } else {
            sentinel.increase()
            willDisplay?(self)
            UIGraphicsBeginImageContextWithOptions(bounds.size, isOpaque, contentsScale)
            guard let context = UIGraphicsGetCurrentContext() else {
                UIGraphicsEndImageContext()
                return
            }
            displaying?(context, bounds.size, {() -> Bool in return false})
            let image = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            contents = image?.cgImage
            didDisplay?(self, true)
        }
    }
    #endif

    #if os(macOS)
    private func displayAsync(_ async: Bool) {
        willDisplay?(self)

        let size = bounds.size
        let isOpaque = self.isOpaque
        let bgColor = self.backgroundColor

        if size.width < 1 || size.height < 1 {
            contents = nil
            didDisplay?(self, true)
            return
        }

        let opaque = isOpaque
        let scale = contentsScale
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let alphaInfo: CGImageAlphaInfo = opaque ? .noneSkipLast : .premultipliedLast

        if async {
            let sentinelValue = sentinel.getValue()
            let isCancelled = { [weak self] in
                return self?.sentinel.getValue() != sentinelValue
            }

            Self.createPoolIfNeed()
            Self.pool?.queue.async { [weak self] in
                guard let self = self else { return }
                if isCancelled() { return }

                let context = CGContext(data: nil, width: Int(size.width * scale), height: Int(size.height * scale), bitsPerComponent: 8, bytesPerRow: 0, space: colorSpace, bitmapInfo: alphaInfo.rawValue)

                if let context = context {
                    context.scaleBy(x: scale, y: scale)

                    // Fill background if opaque
                    if opaque {
                        context.saveGState()
                        if bgColor == nil || (bgColor?.alpha ?? 0) < 1 {
                            context.setFillColor(NSColor.white.cgColor)
                            context.fill(CGRect(origin: .zero, size: size))
                        }
                        if let bg = bgColor {
                            context.setFillColor(bg)
                            context.fill(CGRect(origin: .zero, size: size))
                        }
                        context.restoreGState()
                    }

                    self.displaying?(context, size, isCancelled)

                    if isCancelled() {
                        DispatchQueue.main.async {
                            self.didDisplay?(self, false)
                        }
                        return
                    }

                    let cgImage = context.makeImage()
                    DispatchQueue.main.async {
                        if isCancelled() {
                            self.didDisplay?(self, false)
                            return
                        }
                        self.contents = cgImage
                        self.didDisplay?(self, true)
                    }
                }
            }
        } else {
            let isCancelled = { false }
            let context = CGContext(data: nil, width: Int(size.width * scale), height: Int(size.height * scale), bitsPerComponent: 8, bytesPerRow: 0, space: colorSpace, bitmapInfo: alphaInfo.rawValue)

            if let context = context {
                context.scaleBy(x: scale, y: scale)

                // Fill background if opaque
                if opaque {
                    context.saveGState()
                    if bgColor == nil || (bgColor?.alpha ?? 0) < 1 {
                        context.setFillColor(NSColor.white.cgColor)
                        context.fill(CGRect(origin: .zero, size: size))
                    }
                    if let bg = bgColor {
                        context.setFillColor(bg)
                        context.fill(CGRect(origin: .zero, size: size))
                    }
                    context.restoreGState()
                }

                displaying?(context, size, isCancelled)
                let cgImage = context.makeImage()
                contents = cgImage
                didDisplay?(self, true)
            }
        }
    }

    private func cancelAsyncDisplay() {
        sentinel.increase()
    }
    #endif

    private static func createPoolIfNeed() {
        guard DanmakuAsyncLayer.pool == nil else { return }
        #if os(iOS) || os(tvOS)
        DanmakuAsyncLayer.pool = DanmakuQueuePool(name: "com.DanmakuKit.DanmakuAsynclayer", queueCount: DanmakuAsyncLayer.drawDanmakuQueueCount, qos: .userInteractive)
        #elseif os(macOS)
        DanmakuAsyncLayer.pool = DanmakuQueuePool(name: "DanmakuAsyncLayer.pool", queueCount: DanmakuAsyncLayer.drawDanmakuQueueCount, qos: .userInteractive)
        #endif
    }

    #if os(iOS) || os(tvOS)
    private lazy var queue: DispatchQueue = {
        return DanmakuAsyncLayer.pool?.queue ?? DispatchQueue(label: "com.DanmakuKit.DanmakuAsynclayer")
    }()
    #endif

}
